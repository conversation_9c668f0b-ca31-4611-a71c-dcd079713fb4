use crate::api::user::sys_user::ActiveModel;
use crate::app::AppState;
use crate::common::{Page, PaginationParams};
use crate::entity::prelude::SysUser;
use crate::entity::sys_user;
use crate::error::ApiResult;
use crate::response::ApiResponse;
use crate::valid::{ValidJson, ValidQuery};
use axum::extract::State;
use axum::{Router, debug_handler, routing};
use sea_orm::prelude::Date;
use sea_orm::{
    ActiveModelTrait, ColumnTrait, Condition, DeriveIntoActiveModel, EntityTrait, IntoActiveModel,
};
use sea_orm::{PaginatorTrait, QueryFilter, QueryOrder, QueryTrait};
use serde::Deserialize;
use validator::Validate;

pub fn create_router() -> Router<AppState> {
    Router::new().route("/", routing::get(find_page))
}

#[derive(Debug, Deserialize, Validate)]
#[serde(rename_all = "camelCase")]
pub struct UserQueryParams {
    keyword: Option<String>,
    #[validate(nested)]
    #[serde(flatten)]
    pagination: PaginationParams,
}

// #[tracing::instrument(name = "Query users", skip_all, fields(pay_method = "alipay"))]
#[debug_handler]
async fn find_page(
    State(AppState { db }): State<AppState>,
    ValidQuery(UserQueryParams {
        keyword,
        pagination,
    }): ValidQuery<UserQueryParams>,
) -> ApiResult<ApiResponse<Page<sys_user::Model>>> {
    let paginator = SysUser::find()
        .apply_if(keyword.as_ref(), |query, keyword| {
            query.filter(
                Condition::any()
                    .add(sys_user::Column::Name.contains(keyword))
                    .add(sys_user::Column::Account.contains(keyword)),
            )
        })
        .order_by_desc(sys_user::Column::CreatedAt)
        .paginate(&db, pagination.size);

    let total = paginator.num_items().await?;
    let items = paginator.fetch_page(pagination.page - 1).await?;

    let page = Page::from_pagination(pagination, total, items);

    Ok(ApiResponse::ok("ok", Some(page)))
}

#[derive(Debug, Deserialize, Validate, DeriveIntoActiveModel)]
pub struct UserParams {
    #[validate(length(min = 1, max = 16, message = "姓名长度为1-16"))]
    pub name: String,
    pub gender: String,
    #[validate(length(min = 1, max = 16, message = "账号长度为1-16"))]
    pub account: String,
    #[validate(length(min = 1, max = 16, message = "密码长度为1-16"))]
    pub password: String,
    #[validate(custom(function = "crate::validation::is_mobile_phone"))]
    pub mobile_phone: String,
    pub birthday: Date,
    #[serde(default)]
    pub enabled: bool,
}

#[debug_handler]
async fn create(
    State(AppState { db }): State<AppState>,
    ValidJson(params): ValidJson<UserParams>,
) -> ApiResult<ApiResponse<sys_user::Model>> {
    let active_model = params.into_active_model();
    let result = active_model.insert(&db).await?;

    Ok(ApiResponse::ok("ok", Some(result)))
}
