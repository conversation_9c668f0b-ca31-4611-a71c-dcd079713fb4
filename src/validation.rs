use std::borrow::Cow;
use regex::Regex;
use std::cell::LazyCell;
use std::collections::HashMap;
use validator::ValidationError;

const MOBILE_PHONE_REGEX: LazyCell<Regex> =
    LazyCell::new(|| Regex::new(r"^1[3-9]\d{9}$").expect("Failed to compile mobile phone regex"));

pub fn is_mobile_phone(value: &str) -> Result<(), ValidationError> {
    if MOBILE_PHONE_REGEX.is_match(value) {
        Ok(())
    } else {
        Err(build_validation_error("手机号格式错误"))
    }
}

fn build_validation_error(message: &'static str) -> ValidationError {
    ValidationError {
        code:Cow::from("invalid"),
        message: Some(Cow::from(message)),
        params: HashMap::new()
    }
}