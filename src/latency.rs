use std::fmt::{Di<PERSON><PERSON>, Formatter};
use std::time::Duration;
use axum::http::Response;
use tower_http::trace::OnResponse;
use tracing::Span;

#[derive(Debug, <PERSON><PERSON>, Co<PERSON>)]
pub struct LatencyOnResponse;

impl<B> OnResponse<B> for LatencyOnResponse {
    fn on_response(self, response: &Response<B>, latency: Duration, _span: &Span) {
        tracing::info!(
            latency = %Latency(latency),
            status = %response.status().as_u16(),
            "finished processing request"
        );
    }
}

struct Latency(Duration);

impl Display for Latency {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        if self.0.as_millis() > 0 {
            write!(f, "{}ms", self.0.as_millis())
        } else {
            write!(f, "{}us", self.0.as_micros())
        }
    }
}